from abc import ABC, abstractmethod
from typing import Callable, Awaitable, Dict, List, Optional
import aiohttp
from bs4 import BeautifulSoup
import utils

# --- Type Definitions ---
SubtitleCallback = Callable[[utils.SubtitleFile], Awaitable[None]]
ExtractorCallback = Callable[[utils.ExtractorLink], Awaitable[None]]


class BaseProvider(ABC):
    """
    Base class for all streaming providers.
    Defines the standard interface that all providers should implement.
    """
    
    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.name = "BaseProvider"
        self.main_url = ""
        
    @abstractmethod
    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                         subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """
        Main method to extract streams from the provider.
        
        Args:
            details: TMDB media details dictionary
            season: Season number for TV shows (None for movies)
            episode: Episode number for TV shows (None for movies)
            subtitle_callback: Callback function for subtitle files
            callback: Callback function for stream links
        """
        pass
    
    async def _get_soup(self, url: str, headers: Dict[str, str] = None) -> BeautifulSoup:
        """
        Helper method to fetch and parse HTML content.
        
        Args:
            url: URL to fetch
            headers: Optional headers dictionary
            
        Returns:
            BeautifulSoup object
        """
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
            }
        
        try:
            async with self.session.get(url, headers=headers, ssl=False, timeout=20, allow_redirects=True) as response:
                response.raise_for_status()
                return BeautifulSoup(await response.text(), "html.parser")
        except Exception as e:
            print(f"[{self.name}] Failed to get soup from {url} due to: {e}")
            return BeautifulSoup("", "html.parser")
    
    async def _post_soup(self, url: str, data: Dict, headers: Dict[str, str] = None) -> BeautifulSoup:
        """
        Helper method to POST data and parse HTML response.
        
        Args:
            url: URL to POST to
            data: Data to POST
            headers: Optional headers dictionary
            
        Returns:
            BeautifulSoup object
        """
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
            }

        try:
            async with self.session.post(url, data=data, headers=headers, ssl=False, timeout=20) as response:
                response.raise_for_status()
                return BeautifulSoup(await response.text(), "html.parser")
        except Exception as e:
            print(f"[{self.name}] Failed to post and get soup from {url} due to: {e}")
            return BeautifulSoup("", "html.parser")
    
    def _get_quality_from_height(self, height: int) -> str:
        """
        Helper method to convert height to quality string.
        
        Args:
            height: Video height in pixels
            
        Returns:
            Quality string (e.g., "1080p", "720p", etc.)
        """
        if not isinstance(height, int):
            return "Auto"
        if height >= 2160:
            return "4K"
        if height >= 1080:
            return "1080p"
        if height >= 720:
            return "720p"
        if height >= 480:
            return "480p"
        return "SD"
    
    def __str__(self) -> str:
        return f"{self.name} Provider"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', url='{self.main_url}')>"
