import gzip
import re
import base64
from typing import Any, Dict, List, Optional
import asyncio
import time
import os
from urllib.parse import urlparse

import aiohttp
from fastapi import FastAPI, HTTPException, Query, Response
from starlette.responses import RedirectResponse, StreamingResponse

# Import from the same directory
from . import extractors
from . import utils

from fastapi.middleware.cors import CORSMiddleware


# --- Category Mapping for IPTV ---
category_map = {
    # Special Hand-picked Movie Categories
    "popular_movies": {"name": "⭐ Popular Movies", "id": "998", "endpoint": "/movie/popular", "media_type": "movie"},
    "top_rated_movies": {"name": "🏆 Top Rated Movies", "id": "997", "endpoint": "/movie/top_rated", "media_type": "movie"},
    "upcoming_movies": {"name": "🎬 Upcoming Movies", "id": "996", "endpoint": "/movie/upcoming", "media_type": "movie"},

    # Year-based Movie Categories
    "year_2025_movies": {"name": "Movies of 2025", "id": "2025", "discover_params": {"primary_release_year": 2025}, "media_type": "movie"},
    "year_2024_movies": {"name": "Movies of 2024", "id": "2024", "discover_params": {"primary_release_year": 2024}, "media_type": "movie"},
    "year_2023_movies": {"name": "Movies of 2023", "id": "2023", "discover_params": {"primary_release_year": 2023}, "media_type": "movie"},

    # Genre-based Movie Categories (using TMDB genre IDs)
    "genre_action_movies": {"name": "Action Movies", "id": "28", "discover_params": {"with_genres": "28"}, "media_type": "movie"},
    "genre_adventure_movies": {"name": "Adventure Movies", "id": "12", "discover_params": {"with_genres": "12"}, "media_type": "movie"},
    "genre_animation_movies": {"name": "Animation Movies", "id": "16", "discover_params": {"with_genres": "16"}, "media_type": "movie"},
    "genre_comedy_movies": {"name": "Comedy Movies", "id": "35", "discover_params": {"with_genres": "35"}, "media_type": "movie"},
    "genre_crime_movies": {"name": "Crime Movies", "id": "80", "discover_params": {"with_genres": "80"}, "media_type": "movie"},
    "genre_documentary_movies": {"name": "Documentary Movies", "id": "99", "discover_params": {"with_genres": "99"}, "media_type": "movie"},
    "genre_drama_movies": {"name": "Drama Movies", "id": "18", "discover_params": {"with_genres": "18"}, "media_type": "movie"},
    "genre_family_movies": {"name": "Family Movies", "id": "10751", "discover_params": {"with_genres": "10751"}, "media_type": "movie"},
    "genre_fantasy_movies": {"name": "Fantasy Movies", "id": "14", "discover_params": {"with_genres": "14"}, "media_type": "movie"},
    "genre_history_movies": {"name": "History Movies", "id": "36", "discover_params": {"with_genres": "36"}, "media_type": "movie"},
    "genre_horror_movies": {"name": "Horror Movies", "id": "27", "discover_params": {"with_genres": "27"}, "media_type": "movie"},
    "genre_music_movies": {"name": "Music Movies", "id": "10402", "discover_params": {"with_genres": "10402"}, "media_type": "movie"},
    "genre_mystery_movies": {"name": "Mystery Movies", "id": "9648", "discover_params": {"with_genres": "9648"}, "media_type": "movie"},
    "genre_romance_movies": {"name": "Romance Movies", "id": "10749", "discover_params": {"with_genres": "10749"}, "media_type": "movie"},
    "genre_scifi_movies": {"name": "Science Fiction Movies", "id": "878", "discover_params": {"with_genres": "878"}, "media_type": "movie"},
    "genre_thriller_movies": {"name": "Thriller Movies", "id": "53", "discover_params": {"with_genres": "53"}, "media_type": "movie"},
    "genre_war_movies": {"name": "War Movies", "id": "10752", "discover_params": {"with_genres": "10752"}, "media_type": "movie"},
    "genre_western_movies": {"name": "Western Movies", "id": "37", "discover_params": {"with_genres": "37"}, "media_type": "movie"},

    # Special Hand-picked TV Show Categories
    "popular_tv": {"name": "⭐ Popular TV Shows", "id": "tv_998", "endpoint": "/tv/popular", "media_type": "tv"},
    "top_rated_tv": {"name": "🏆 Top Rated TV Shows", "id": "tv_997", "endpoint": "/tv/top_rated", "media_type": "tv"},

    # Genre-based TV Show Categories (using TMDB genre IDs)
    "genre_action_adventure_tv": {"name": "Action & Adventure TV", "id": "tv_10759", "discover_params": {"with_genres": "10759"}, "media_type": "tv"},
    "genre_animation_tv": {"name": "Animation TV", "id": "tv_16", "discover_params": {"with_genres": "16"}, "media_type": "tv"},
    "genre_comedy_tv": {"name": "Comedy TV", "id": "tv_35", "discover_params": {"with_genres": "35"}, "media_type": "tv"},
    "genre_crime_tv": {"name": "Crime TV", "id": "tv_80", "discover_params": {"with_genres": "80"}, "media_type": "tv"},
    "genre_documentary_tv": {"name": "Documentary TV", "id": "tv_99", "discover_params": {"with_genres": "99"}, "media_type": "tv"},
    "genre_drama_tv": {"name": "Drama TV", "id": "tv_18", "discover_params": {"with_genres": "18"}, "media_type": "tv"},
    "genre_family_tv": {"name": "Family TV", "id": "tv_10751", "discover_params": {"with_genres": "10751"}, "media_type": "tv"},
    "genre_kids_tv": {"name": "Kids TV", "id": "tv_10762", "discover_params": {"with_genres": "10762"}, "media_type": "tv"},
    "genre_mystery_tv": {"name": "Mystery TV", "id": "tv_9648", "discover_params": {"with_genres": "9648"}, "media_type": "tv"},
    "genre_news_tv": {"name": "News TV", "id": "tv_10763", "discover_params": {"with_genres": "10763"}, "media_type": "tv"},
    "genre_reality_tv": {"name": "Reality TV", "id": "tv_10764", "discover_params": {"with_genres": "10764"}, "media_type": "tv"},
    "genre_scifi_fantasy_tv": {"name": "Sci-Fi & Fantasy TV", "id": "tv_10765", "discover_params": {"with_genres": "10765"}, "media_type": "tv"},
    "genre_soap_tv": {"name": "Soap TV", "id": "tv_10766", "discover_params": {"with_genres": "10766"}, "media_type": "tv"},
    "genre_talk_tv": {"name": "Talk TV", "id": "tv_10767", "discover_params": {"with_genres": "10767"}, "media_type": "tv"},
    "genre_war_politics_tv": {"name": "War & Politics TV", "id": "tv_10768", "discover_params": {"with_genres": "10768"}, "media_type": "tv"},
    "genre_western_tv": {"name": "Western TV", "id": "tv_37", "discover_params": {"with_genres": "37"}, "media_type": "tv"},
}


# --- Helper: convert SRT to VTT ---
def srt_to_vtt(srt_content: str) -> str:
    """Converts SRT subtitle format to VTT format."""
    vtt = "WEBVTT\n\n"
    vtt += re.sub(r'(\d{2}:\d{2}:\d{2}),(\d{3})', r'\1.\2', srt_content)
    vtt = re.sub(r'^\d+\s*$', '', vtt, flags=re.MULTILINE)
    return vtt.strip()

# --- Helper: Get URL Extension ---
def get_url_extension(url: str) -> str:
    """
    Extracts the file extension from a URL's path.
    Returns 'm3u8' as a fallback if no extension is found.
    """
    try:
        path = urlparse(url).path
        ext = os.path.splitext(path)[1]
        if ext:
            return ext.lstrip('.')  # Remove the leading dot
    except Exception:
        pass
    return "m3u8" # Fallback extension

# --- API Class ---
class SoraStreamAPI:
    """Handles logic for fetching data from TMDB and extracting stream links."""
    def __init__(self, tmdb_api_key: str, session: aiohttp.ClientSession):
        self.tmdb_api_key = tmdb_api_key
        self.session = session
        self.extractor = extractors.SoraExtractor(session)
        self.TMDB_API_URL = "https://api.themoviedb.org/3"
        self.TMDB_IMAGE_URL = "https://image.tmdb.org/t/p/original"
        self.TMDB_IMAGE_URL_W500 = "https://image.tmdb.org/t/p/w500"


    async def search(self, query: str) -> List[Dict[str, Any]]:
        """Searches TMDB for movies and TV shows."""
        url = f"{self.TMDB_API_URL}/search/multi?api_key={self.tmdb_api_key}&query={query}"
        async with self.session.get(url) as r:
            if r.status != 200: return []
            data = await r.json()
            return [x for x in data.get("results", []) if x.get("media_type") in ("movie", "tv")]

    async def get_media_details(self, media_type: str, tmdb_id: int) -> Optional[Dict[str, Any]]:
        """Fetches detailed information for a movie or TV show from TMDB."""
        url = f"{self.TMDB_API_URL}/{media_type}/{tmdb_id}?api_key={self.tmdb_api_key}&append_to_response=external_ids,videos"
        async with self.session.get(url) as r:
            return await r.json() if r.status == 200 else None

    # New method specifically for fetching TV season details.
    async def get_season_details(self, series_id: int, season_number: int) -> Optional[Dict[str, Any]]:
        """Fetches detailed information for a specific TV show season from TMDB."""
        url = f"{self.TMDB_API_URL}/tv/{series_id}/season/{season_number}?api_key={self.tmdb_api_key}"
        async with self.session.get(url) as r:
            return await r.json() if r.status == 200 else None

    async def _fetch_tmdb_pages(self, url: str, pages: int):
        """Helper to fetch multiple pages from a TMDB endpoint."""
        async def fetch_page(page):
            paginated_url = f"{url}&page={page}"
            async with self.session.get(paginated_url) as r:
                if r.status == 200:
                    data = await r.json()
                    return data.get("results", [])
                return []

        tasks = [fetch_page(page) for page in range(1, pages + 1)]
        page_results = await asyncio.gather(*tasks)
        return [item for page_list in page_results for item in page_list]

    async def discover_media(self, media_type: str, endpoint: Optional[str] = None, discover_params: Optional[Dict[str, Any]] = None, pages: int = 10) -> List[Dict[str, Any]]:
        """Fetches multiple pages of media (movies or TV) from a specific endpoint or using discover parameters."""
        if endpoint:
            base_url = f"{self.TMDB_API_URL}{endpoint}?api_key={self.tmdb_api_key}"
        elif discover_params:
            params = "&".join([f"{key}={value}" for key, value in discover_params.items()])
            base_url = f"{self.TMDB_API_URL}/discover/{media_type}?api_key={self.tmdb_api_key}&{params}&sort_by=popularity.desc"
        else:
            base_url = f"{self.TMDB_API_URL}/{media_type}/popular?api_key={self.tmdb_api_key}"
        return await self._fetch_tmdb_pages(base_url, pages)


    async def get_logo(self, media_type: str, tmdb_id: int) -> Optional[str]:
        """Fetches the English logo for a media item, or the first available one."""
        url = f"{self.TMDB_API_URL}/{media_type}/{tmdb_id}/images?api_key={self.tmdb_api_key}"
        async with self.session.get(url) as r:
            if r.status != 200: return None
            data = await r.json()
            logos = data.get("logos", [])
            if not logos: return None
            english_logo = next((logo for logo in logos if logo.get("iso_639_1") == "en"), None)
            if english_logo and english_logo.get('file_path'):
                 return f"{self.TMDB_IMAGE_URL}{english_logo['file_path']}"
            first_logo = logos[0]
            if first_logo and first_logo.get('file_path'):
                return f"{self.TMDB_IMAGE_URL}{first_logo['file_path']}"
            return None

    async def get_trending(self, media_type: str, time_window: str) -> List[Dict[str, Any]]:
        """Gets trending media from TMDB."""
        url = f"{self.TMDB_API_URL}/trending/{media_type}/{time_window}?api_key={self.tmdb_api_key}"
        async with self.session.get(url) as r:
            if r.status != 200: return []
            return (await r.json()).get("results", [])

    async def get_providers(self, media_type: str, region: str) -> List[Dict[str, Any]]:
        """Gets watch providers for a region from TMDB."""
        url = f"{self.TMDB_API_URL}/watch/providers/{media_type}?api_key={self.tmdb_api_key}&watch_region={region}"
        async with self.session.get(url) as r:
            if r.status != 200: return []
            return (await r.json()).get("results", [])

    async def discover_by_providers(self, media_type: str, provider_ids: List[int], region: str) -> Dict[str, List[Dict[str, Any]]]:
        """Discovers content by specified provider IDs."""
        all_providers = await self.get_providers(media_type, region)
        id_to_name = {p['provider_id']: p['provider_name'] for p in all_providers}
        
        async def fetch(pid):
            url = f"{self.TMDB_API_URL}/discover/{media_type}?api_key={self.tmdb_api_key}&watch_region={region}&with_watch_providers={pid}&sort_by=popularity.desc"
            async with self.session.get(url) as r:
                data = await r.json() if r.status == 200 else {}
                return pid, data.get("results", [])
        results = await asyncio.gather(*(fetch(pid) for pid in provider_ids))
        return {id_to_name[pid]: res for pid, res in results if id_to_name.get(pid) and res}

    async def get_streams(self, details: Dict, season: Optional[int] = None, episode: Optional[int] = None):
        """Gathers all available streams and subtitles from enabled providers."""
        streams, subs = [], []
        async def sub_cb(s): subs.append(s)
        async def str_cb(s): streams.append(s)
        
        # All provider tasks are gathered here
        provider_tasks = [
            #self.extractor.invoke_vidsrc(details, season, episode, sub_cb, str_cb),
            self.extractor.invoke_arabseed(details, season, episode, sub_cb, str_cb), # Added the new extractor
        ]
        await asyncio.gather(*provider_tasks)
        
        # Sort streams by quality (highest first)
        streams.sort(key=lambda x: x.quality if hasattr(x, 'quality') and x.quality is not None else 0, reverse=True)
        return {"streams": streams, "subtitles": subs}

# --- FastAPI App ---
app = FastAPI(title="SoraStream API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", include_in_schema=False)
async def root():
    """Redirects to the API documentation."""
    return RedirectResponse(url="/docs")

# --- ORIGINAL MEDIA ENDPOINTS (UNTOUCHED) ---

@app.get("/search", tags=["Media"])
async def search_media(query: str = Query(..., min_length=1, description="The search query.")):
    """Search for movies and TV shows."""
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        if not query: raise HTTPException(status_code=400, detail="A search query is required.")
        res = await api.search(query)
        if not res: raise HTTPException(status_code=404, detail="No results found.")
        return {"results": res}

@app.get("/media/{media_type}/{tmdb_id}", tags=["Media"])
async def media_details(media_type: str, tmdb_id: int):
    """Get details for a specific movie or TV show."""
    if media_type not in ("movie", "tv"):
        raise HTTPException(status_code=400, detail="Invalid media type. Must be 'movie' or 'tv'.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details(media_type, tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Media not found.")
        return details

@app.get("/logo/{media_type}/{tmdb_id}", tags=["Media"])
async def get_media_logo(media_type: str, tmdb_id: int):
    """Get the logo for a movie or TV show."""
    if media_type not in ("movie", "tv"):
        raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        logo_url = await api.get_logo(media_type, tmdb_id)
        if not logo_url: raise HTTPException(status_code=404, detail="No logo found.")
        return RedirectResponse(url=logo_url)

@app.get("/trending/{media_type}", tags=["Discover & Providers"])
async def trending(media_type: str, time_window: str = Query("day", enum=["day", "week"])):
    """Get trending content."""
    if media_type not in ("all", "movie", "tv"):
        raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        return {"results": await api.get_trending(media_type, time_window)}

@app.get("/providers/{media_type}", tags=["Discover & Providers"])
async def providers(media_type: str, region: str = Query("US", min_length=2, max_length=2)):
    """Get a list of watch providers for a given region."""
    if media_type not in ("movie", "tv"):
        raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        return {"providers": await api.get_providers(media_type, region)}

@app.get("/discover/provider/{media_type}", tags=["Discover & Providers"])
async def discover_platform(media_type: str, providers: str = Query(..., description="Comma-separated list of provider IDs."), region: str = Query("US")):
    """Discover content from specific streaming platforms."""
    if media_type not in ("movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    try: pids = [int(x.strip()) for x in providers.split(",")]
    except ValueError: raise HTTPException(status_code=400, detail="Invalid provider IDs.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        res = await api.discover_by_providers(media_type, pids, region)
        if not res: raise HTTPException(status_code=404, detail="No content found for the specified providers.")
        return res

@app.get("/streams/movie/{tmdb_id}", tags=["Streams & Subtitles"])
async def movie_streams(tmdb_id: int):
    """Get streams and subtitles for a movie."""
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("movie", tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Movie not found.")
        # For movies, season and episode are typically not applicable for stream lookup, passing None.
        data = await api.get_streams(details, season=None, episode=None)
        if not data["streams"] and not data["subtitles"]: raise HTTPException(status_code=404, detail="No streams or subtitles could be found.")
        return data

@app.get("/streams/tv/{tmdb_id}/{season}/{episode}", tags=["Streams & Subtitles"])
async def tv_streams(tmdb_id: int, season: int, episode: int):
    """Get streams and subtitles for a TV show episode."""
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("tv", tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Show not found.")
        data = await api.get_streams(details, season, episode)
        if not data["streams"] and not data["subtitles"]: raise HTTPException(status_code=404, detail="No streams or subtitles could be found.")
        return data

@app.get("/subtitles/{file_id}.vtt", tags=["Streams & Subtitles"])
async def subtitles(file_id: str):
    """Proxies and formats subtitle files from different sources into VTT format."""
    async with aiohttp.ClientSession() as session:
        if file_id.startswith("new_"):
            try:
                real_file_id = int(file_id.replace("new_", ""))
                # Use the opensubtitles API key directly
                opensubtitles_api_key = "uAs2wKWO43KbIpXMDQV40I0q5FeTPr9Y"
                headers = {'Api-Key': opensubtitles_api_key, 'User-Agent': 'SoraStream v1', 'Content-Type': 'application/json', 'Accept': 'application/json'}
                body = {'file_id': real_file_id}
                async with session.post("https://api.opensubtitles.com/api/v1/download", headers=headers, json=body) as r:
                    r.raise_for_status()
                    download_link = (await r.json()).get("link")
                    if not download_link: raise HTTPException(status_code=500, detail="API did not provide a subtitle download link.")
                async with session.get(download_link) as sub_response:
                    sub_response.raise_for_status()
                    srt_content = await sub_response.text(encoding="utf-8", errors="ignore")
                vtt_content = srt_to_vtt(srt_content)
                return Response(content=vtt_content, media_type="text/vtt; charset=utf-8")
            except Exception as e: raise HTTPException(status_code=502, detail=f"Failed to process subtitle from new API: {e}")
        else:
            try:
                url = f"https://dl.opensubtitles.org/en/download/file/{file_id}"
                headers = {"User-Agent": "SubDB/1.0"}
                async with session.get(url, headers=headers) as r:
                    r.raise_for_status()
                    content_bytes = await r.read()
                try: srt_content = gzip.decompress(content_bytes).decode("utf-8", "ignore")
                except (gzip.BadGzipFile, OSError): srt_content = content_bytes.decode("utf-8", "ignore")
                if not srt_content.strip(): raise ValueError("Subtitle file is empty.")
                vtt_content = srt_to_vtt(srt_content)
                return Response(content=vtt_content, media_type="text/vtt; charset=utf-8")
            except Exception as e: raise HTTPException(status_code=502, detail=f"Legacy subtitle processing failed: {e}")


# --- IPTV Section ---

@app.get("/player_api.php", tags=["IPTV"])
async def player_api(
    username: str = Query(..., description="Your IPTV username."),
    password: str = Query(..., description="Your IPTV password."),
    action: Optional[str] = Query(None, description="The action to perform."),
    vod_id: Optional[int] = Query(None, description="The VOD ID (TMDB ID for movies)."),
    series_id: Optional[int] = Query(None, description="The Series ID (TMDB ID for TV shows)."),
    category_id: Optional[str] = Query(None, description="The category ID to filter content.")
):
    """
    Handles all IPTV player API requests.
    """
    if username != "test" or password != "test":
        raise HTTPException(status_code=401, detail="Invalid credentials.")

    if action is None:
        return {
            "user_info": {"username": "test", "password": "test", "auth": 1, "status": "Active", "exp_date": "1754761892", "is_trial": "0", "active_cons": "0", "created_at": "1659461146", "max_connections": "1", "allowed_output_formats": ["m3u8", "ts"]},
            "server_info": {"url": "mhav1.com", "port": "80", "https_port": "25463", "server_protocol": "http", "rtmp_port": "25462", "timezone": "Africa/Cairo", "timestamp_now": int(time.time()), "time_now": time.strftime("%Y-%m-%d %H:%M:%S", time.gmtime()), "process": True},
        }
    
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)

        if action == "get_vod_categories":
            return [{"category_id": cat["id"], "category_name": cat["name"], "parent_id": 0} for cat in category_map.values() if cat.get("media_type") == "movie"]

        if action == "get_series_categories":
            return [{"category_id": cat["id"], "category_name": cat["name"], "parent_id": 0} for cat in category_map.values() if cat.get("media_type") == "tv"]

        if action == "get_vod_streams":
            category = next((cat for cat in category_map.values() if cat["id"] == category_id and cat["media_type"] == "movie"), None)
            
            if category:
                movies = await api.discover_media("movie", endpoint=category.get("endpoint"), discover_params=category.get("discover_params"))
            elif not category_id or category_id == "*":
                 movies = await api.discover_media("movie", endpoint="/movie/popular")
            else:
                movies = []

            vod_streams = []
            for i, movie in enumerate(movies):
                if movie.get("id") and movie.get("title"):
                    vod_streams.append({
                        "num": i + 1, "name": movie.get("title"), "stream_type": "movie", "stream_id": movie.get("id"),
                        "stream_icon": f"{api.TMDB_IMAGE_URL_W500}{movie.get('poster_path')}" if movie.get('poster_path') else "",
                        "rating": str(movie.get("vote_average")), "rating_5based": movie.get("vote_average") / 2,
                        "added": str(int(time.time())), "is_adult": "0", "category_id": category_id,
                        "container_extension": "m3u8", "custom_sid": "", "direct_source": "",
                    })
            return vod_streams
            
        if action == "get_series":
            category = next((cat for cat in category_map.values() if cat["id"] == category_id and cat["media_type"] == "tv"), None)

            if category:
                series = await api.discover_media("tv", endpoint=category.get("endpoint"), discover_params=category.get("discover_params"))
            elif not category_id or category_id == "*":
                series = await api.discover_media("tv", endpoint="/tv/popular")
            else:
                series = []

            series_streams = []
            for i, show in enumerate(series):
                if show.get("id") and show.get("name"):
                    series_streams.append({
                        "num": i + 1, "name": show.get("name"), "series_id": show.get("id"),
                        "cover": f"{api.TMDB_IMAGE_URL_W500}{show.get('poster_path')}" if show.get('poster_path') else "",
                        "plot": show.get("overview"), "cast": "", "director": "", "genre": "",
                        "releaseDate": show.get("first_air_date"), "last_modified": str(int(time.time())),
                        "rating": str(show.get("vote_average")), "rating_5based": show.get("vote_average") / 2,
                        "youtube_trailer": "", "episode_run_time": "", "category_id": category_id,
                    })
            return series_streams

        if action == "get_vod_info":
            if not vod_id: raise HTTPException(status_code=400, detail="Missing 'vod_id' parameter.")
            details = await api.get_media_details("movie", vod_id)
            if not details: raise HTTPException(status_code=404, detail="Movie not found.")

            # Dynamically find the stream extension
            stream_data = await api.get_streams(details, season=None, episode=None)
            extension = "m3u8"  # Default fallback
            if stream_data.get("streams"):
                best_stream_url = stream_data["streams"][0].url
                extension = get_url_extension(best_stream_url)

            trailer_key = ""
            if details.get("videos") and details["videos"].get("results"):
                for video in details["videos"]["results"]:
                    if video.get("site") == "YouTube" and video.get("type") == "Trailer": trailer_key = video.get("key"); break
            
            runtime_mins = details.get("runtime", 0)
            return {
                "info": {
                    "movie_image": f"{api.TMDB_IMAGE_URL_W500}{details.get('poster_path')}", "tmdb_id": str(details.get("id")),
                    "youtube_trailer": trailer_key, "genre": ", ".join([g["name"] for g in details.get("genres", [])]),
                    "plot": details.get("overview"), "cast": "", "rating": str(details.get("vote_average")), "director": "",
                    "releasedate": details.get("release_date"), "backdrop_path": [f"{api.TMDB_IMAGE_URL}{details.get('backdrop_path')}"] if details.get('backdrop_path') else [],
                    "duration_secs": runtime_mins * 60 if runtime_mins else 0,
                    "duration": time.strftime('%H:%M:%S', time.gmtime(runtime_mins * 60)) if runtime_mins else "00:00:00",
                    "video": {}, "audio": {}, "bitrate": 0,
                },
                "movie_data": {
                    "stream_id": vod_id, "name": details.get("title"), "added": str(int(time.time())), "category_id": category_id,
                    "container_extension": extension, # Use the dynamic extension
                    "custom_sid": "",
                    "direct_source": f"/movie/{username}/{password}/{vod_id}.{extension}", # Use the dynamic extension
                },
            }

        if action == "get_series_info":
            if not series_id: raise HTTPException(status_code=400, detail="Missing 'series_id' parameter.")
            series_details = await api.get_media_details("tv", series_id)
            if not series_details: raise HTTPException(status_code=404, detail="Series not found.")

            # 1. Format the 'info' object
            trailer_key = ""
            if series_details.get("videos") and series_details["videos"].get("results"):
                for video in series_details["videos"]["results"]:
                    if video.get("site") == "YouTube" and video.get("type") == "Trailer": trailer_key = video.get("key"); break

            run_times = series_details.get("episode_run_time", [])
            episode_run_time = str(run_times[0]) if run_times else "0"

            info_object = {
                "name": series_details.get("name"),
                "cover": f"{api.TMDB_IMAGE_URL_W500}{series_details.get('poster_path')}" if series_details.get('poster_path') else "",
                "plot": series_details.get("overview"),
                "cast": ", ".join([creator['name'] for creator in series_details.get('created_by', [])]),
                "director": "",
                "genre": ", ".join([g["name"] for g in series_details.get("genres", [])]),
                "releaseDate": series_details.get("first_air_date"),
                "last_modified": str(int(time.time())),
                "rating": str(series_details.get("vote_average")),
                "rating_5based": round(series_details.get("vote_average", 0) / 2, 1),
                "backdrop_path": [f"{api.TMDB_IMAGE_URL}{series_details.get('backdrop_path')}"] if series_details.get('backdrop_path') else [],
                "youtube_trailer": trailer_key,
                "episode_run_time": episode_run_time,
                "category_id": category_id,
            }

            # 2. Format the 'seasons' and 'episodes' objects
            seasons_list = []
            episodes_data = {}

            for season in series_details.get("seasons", []):
                season_number = season.get("season_number")
                if season_number is None: continue

                seasons_list.append({
                    "air_date": season.get("air_date"),
                    "episode_count": season.get("episode_count"),
                    "id": season.get("id"),
                    "name": season.get("name"),
                    "overview": season.get("overview"),
                    "season_number": season_number,
                    "cover": f"{api.TMDB_IMAGE_URL_W500}{season.get('poster_path')}" if season.get('poster_path') else "",
                    "cover_big": f"{api.TMDB_IMAGE_URL}{season.get('poster_path')}" if season.get('poster_path') else ""
                })

                season_details_with_eps = await api.get_season_details(series_id, season_number)
                if not season_details_with_eps or not season_details_with_eps.get("episodes"): continue

                season_episodes_list = []
                for episode in season_details_with_eps.get("episodes", []):
                    episode_num = episode.get('episode_number')
                    runtime = episode.get("runtime", 0) or 0
                    
                    # Fetch streams for each episode to find the correct file extension.
                    stream_data = await api.get_streams(series_details, season=season_number, episode=episode_num)
                    extension = "m3u8"  # Default fallback
                    if stream_data.get("streams"):
                        best_stream_url = stream_data["streams"][0].url
                        extension = get_url_extension(best_stream_url)
                    
                    # IPTV players use this ID to construct the stream URL. e.g., /series/user/pass/{id}.{container_extension}
                    composite_id = f"{series_id}:{season_number}:{episode_num}"
                    
                    season_episodes_list.append({
                        "id": composite_id,
                        "episode_num": episode_num,
                        "title": episode.get('name', f"Episode {episode_num}"),
                        "container_extension": extension, # Use the dynamic extension
                        "info": {
                            "releasedate": episode.get("air_date"),
                            "plot": episode.get("overview"),
                            "duration_secs": runtime * 60,
                            "duration": time.strftime('%H:%M:%S', time.gmtime(runtime * 60)),
                            "movie_image": f"{api.TMDB_IMAGE_URL_W500}{episode.get('still_path')}" if episode.get('still_path') else "",
                            "video": {}, "audio": {}, "bitrate": 0,
                            "rating": str(episode.get("vote_average")),
                            "season": season_number,
                            "tmdb_id": episode.get("id")
                        },
                        "custom_sid": "",
                        "added": str(int(time.time())),
                        "season": season_number,
                        "direct_source": ""
                    })
                episodes_data[str(season_number)] = season_episodes_list
            
            # 3. Assemble final response
            return {
                "seasons": seasons_list,
                "info": info_object,
                "episodes": episodes_data
            }


    raise HTTPException(status_code=400, detail="Invalid action.")

@app.get("/movie/{username}/{password}/{vod_id}.{container_extension}", tags=["IPTV"])
async def stream_movie(username: str, password: str, vod_id: int, container_extension: str):
    """
    Fetches the best quality stream URL for the VOD ID, proxies it, and redirects the client.
    The 'container_extension' is ignored but kept for compatibility with IPTV players.
    """
    if username != "test" or password != "test": raise HTTPException(status_code=401, detail="Invalid credentials.")

    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("movie", vod_id)
        if not details: raise HTTPException(status_code=404, detail="Movie not found.")

        stream_data = await api.get_streams(details, season=None, episode=None)
        if not stream_data.get("streams"): raise HTTPException(status_code=404, detail="No streams found for this movie.")

        best_quality_url = stream_data["streams"][0].url
        # --- PROXY RESTORED ---
        target_url = f"https://stream-proxy-pearl.vercel.app/api/proxy?target={best_quality_url}"
        return RedirectResponse(url=target_url)

@app.get("/series/{username}/{password}/{series_info}.{container_extension}", tags=["IPTV"])
async def stream_series(username: str, password: str, series_info: str, container_extension: str):
    """
    Fetches the best quality stream URL for the Series, proxies it, and redirects the client.
    The 'container_extension' is ignored but kept for compatibility with IPTV players.
    """
    if username != "test" or password != "test":
        raise HTTPException(status_code=401, detail="Invalid credentials.")

    try:
        tmdb_id, season, episode = map(int, series_info.split(':'))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid series info format. Expected tmdb_id:season:episode.")

    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("tv", tmdb_id)
        if not details:
            raise HTTPException(status_code=404, detail="TV show not found.")

        stream_data = await api.get_streams(details, season=season, episode=episode)
        if not stream_data.get("streams"):
            raise HTTPException(status_code=404, detail="No streams found for this episode.")

        best_quality_url = stream_data["streams"][0].url
        # --- PROXY RESTORED ---
        target_url = f"https://stream-proxy-pearl.vercel.app/api/proxy?target={best_quality_url}"
        return RedirectResponse(url=target_url)