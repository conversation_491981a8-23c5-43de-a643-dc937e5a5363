import asyncio
import re
from urllib.parse import urlparse
from typing import Any, Awaitable, Callable, Dict, List, Optional
from bs4 import BeautifulSoup
import aiohttp
import traceback
from dataclasses import dataclass

import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback

class DizikoreaProvider(BaseProvider):
    """
    Final version that correctly mimics the advanced, multi-step player loading
    process as revealed by the Selenium diagnostic script.
    """
    def __init__(self, session: aiohttp.ClientSession):
        super().__init__(session)
        self.main_url = "https://dizikorea.pw"
        self.name = "Dizikorea"
        self.lang = "tr"

    async def _handle_player_source(self, player_url: str, referer_url: str, source_name: str, callback: ExtractorCallback):
        """
        Handles playerkorea and vidmoly sources by finding the obfuscated script,
        extracting the relative M3U8 path, and constructing the full URL.
        This mimics the "click -> intercept" logic of the Selenium script.
        """
        print(f"[<PERSON><PERSON><PERSON><PERSON>] Handling player source for '{source_name}'")
        try:
            # Step 1: Fetch the player page with the correct Dizikorea referer
            async with aiohttp.ClientSession() as player_session:
                async with player_session.get(player_url, ssl=False, headers={'Referer': referer_url}) as response:
                    if response.status != 200: return
                    player_html = await response.text()

            # Step 2: Find the packed JavaScript in the player's HTML
            packer_match = re.search(r"eval\(function\(p,a,c,k,e,d\)\s*\{(.*)\}\((.*)\)\)", player_html, re.DOTALL)
            if not packer_match:
                print(f"[Dizikorea] ERROR: Packer script not found for '{source_name}' at {player_url}")
                return
            
            # Step 3: De-obfuscate the script to reveal its contents
            unpacked_js = self._unpack_packer(packer_match.group(0))
            if not unpacked_js:
                print(f"[Dizikorea] ERROR: Failed to unpack script for '{source_name}'")
                return

            # Step 4: Extract the relative M3U8 file path from the clean JavaScript
            # This is the information the player would use after the "click"
            source_match = re.search(r'file\s*:\s*[\'"](.*?)[\'"]', unpacked_js)
            if not source_match:
                print(f"[Dizikorea] ERROR: 'file' path not found in unpacked script for '{source_name}'")
                return
            
            relative_path = source_match.group(1).replace("\\/", "/")
            
            # Step 5: Construct the full, absolute M3U8 URL
            player_base_url = f"{urlparse(player_url).scheme}://{urlparse(player_url).netloc}"
            final_m3u8_url = f"{player_base_url}/{relative_path}"
            
            print(f"[Dizikorea] SUCCESS! Extracted M3U8 for '{source_name}': {final_m3u8_url}")

            await callback(utils.ExtractorLink(
                name=f"{source_name} Stream",
                url=final_m3u8_url,
                referer=player_base_url, 
                quality=1080, # Assume HD
                is_m3u8=True
            ))

        except aiohttp.ClientConnectorError:
            # Handle cases where the player site (e.g., vidmoly) is down
             print(f"[Dizikorea] DNS/Connection error for '{source_name}'. The site may be offline.")
        except Exception:
            traceback.print_exc()

    async def load_links(self, data_url: str, subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> bool:
        """ Fetches source buttons from the main page and calls the correct handler for each. """
        try:
            async with aiohttp.ClientSession() as page_session:
                 async with page_session.get(data_url, ssl=False, headers={'Referer': self.main_url}) as response:
                    if response.status != 200: return False
                    document = BeautifulSoup(await response.text(), "html.parser")

            source_buttons = document.select("div.series-watch-alternatives button[data-hhs]")
            if not source_buttons: return False

            tasks = []
            for button in source_buttons:
                source_url = button.get("data-hhs")
                source_name = button.get("title", "Dizikorea")
                
                # Based on the Selenium script, playerkorea and vidmoly use the same logic
                if source_url and ("playerkorea" in source_url or "vidmoly" in source_url):
                    final_player_url = utils.fix_url(source_url, self.main_url)
                    # Pass the main page URL as the referer, which is the crucial step
                    tasks.append(self._handle_player_source(final_player_url, data_url, source_name, callback))
                else:
                    print(f"[Dizikorea] Skipping '{source_name}': Not a supported player type.")
            
            if tasks:
                await asyncio.gather(*tasks)

            return True
            
        except Exception:
            traceback.print_exc()
            return False

    def _unpack_packer(self, script_text: str) -> Optional[str]:
        """ Unpacks the common 'eval(function(p,a,c,k,e,d))' JS obfuscation. """
        try:
            match = re.search(r"}\s*\('(.*)',\s*(\d+),\s*(\d+),\s*'(.*?)'\.split\('\|'\)", script_text)
            if not match: return None
            
            p, a, c, k_str = match.groups()
            a, c = int(a), int(c)
            k = k_str.split('|')

            def base_n(num, b, numerals="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"):
                if num == 0: return "0"
                base_n_str = ""
                while num > 0:
                    num, i = divmod(num, b)
                    base_n_str = numerals[i] + base_n_str
                return base_n_str or "0"

            while c > 0:
                c -= 1
                if k[c]:
                    p = re.sub('\\b' + base_n(c, a) + '\\b', k[c], p)
            return p
        except Exception:
            return None

    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                         subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """
        Extract streams from Dizikorea.
        This method adapts the existing load_links method to the standard interface.
        """
        print(f"[{self.name}] Trying Dizikorea...")

        # For Dizikorea, we need to construct the data URL from the details
        # This is a simplified approach - you may need to adjust based on how Dizikorea URLs work
        title = details.get("title") or details.get("name") or details.get("original_title") or details.get("original_name")
        if not title:
            print(f"[{self.name}] Could not find title in TMDB details.")
            return

        # Construct a search URL or episode URL based on the title and episode info
        # This is a placeholder - you'll need to implement the actual URL construction logic
        # based on how Dizikorea structures its URLs
        try:
            # For now, we'll skip the actual implementation since it requires
            # understanding Dizikorea's URL structure
            print(f"[{self.name}] URL construction for Dizikorea needs to be implemented based on site structure.")
            return
        except Exception as e:
            print(f"[{self.name}] Error in get_streams: {e}")
            traceback.print_exc()