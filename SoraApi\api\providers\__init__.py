"""
Provider registry for all streaming providers.
"""

from typing import List, Type, Optional
import aiohttp

from .base_provider import BaseProvider
from .vidsrc import VidSrcProvider
from .arabseed import ArabSeedProvider
from .dizikorea.dizikorea import <PERSON>zikoreaProvider
from .dramadrip.dramadrip import DramaDripProvider
from .kisskh.kisskh import KisskhProvider


class ProviderRegistry:
    """
    Registry for managing all available streaming providers.
    """

    def __init__(self):
        self._provider_classes = [
            VidSrcProvider,
            ArabSeedProvider,
            DizikoreaProvider,
            DramaDripProvider,
            KisskhProvider,
        ]

    def get_all_providers(self, session: aiohttp.ClientSession) -> List[BaseProvider]:
        """
        Get instances of all registered providers.

        Args:
            session: aiohttp ClientSession to use for all providers

        Returns:
            List of provider instances
        """
        return [provider_class(session) for provider_class in self._provider_classes]

    def get_provider_by_name(self, name: str, session: aiohttp.ClientSession) -> Optional[BaseProvider]:
        """
        Get a specific provider by name.

        Args:
            name: Name of the provider
            session: aiohttp ClientSession to use

        Returns:
            Provider instance or None if not found
        """
        for provider_class in self._provider_classes:
            provider = provider_class(session)
            if provider.name.lower() == name.lower():
                return provider
        return None

    def get_provider_names(self) -> List[str]:
        """
        Get names of all registered providers.

        Returns:
            List of provider names
        """
        # Create temporary instances to get names
        temp_session = None
        try:
            import aiohttp
            temp_session = aiohttp.ClientSession()
            return [provider_class(temp_session).name for provider_class in self._provider_classes]
        except:
            # Fallback to class names if we can't create instances
            return [cls.__name__.replace('Provider', '') for cls in self._provider_classes]
        finally:
            if temp_session:
                import asyncio
                try:
                    asyncio.create_task(temp_session.close())
                except:
                    pass


# Global registry instance
registry = ProviderRegistry()

# Convenience exports
__all__ = [
    'BaseProvider',
    'VidSrcProvider',
    'ArabSeedProvider',
    'DizikoreaProvider',
    'DramaDripProvider',
    'KisskhProvider',
    'ProviderRegistry',
    'registry'
]