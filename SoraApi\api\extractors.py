import aiohttp
import asyncio
from typing import Callable, Awaitable, Dict, List, Optional
from . import utils
from .providers import registry

# --- Type Definitions ---
SubtitleCallback = Callable[[utils.SubtitleFile], Awaitable[None]]
ExtractorCallback = Callable[[utils.ExtractorLink], Awaitable[None]]


class SoraExtractor:
    """
    Simplified extractor that coordinates multiple streaming providers.
    All provider logic has been moved to separate modular providers.
    """
    
    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.providers = registry.get_all_providers(session)
        
    async def get_streams(self, details: Dict, season: Optional[int] = None, episode: Optional[int] = None):
        """
        Gathers all available streams and subtitles from all enabled providers.
        
        Args:
            details: TMDB media details dictionary
            season: Season number for TV shows (None for movies)
            episode: Episode number for TV shows (None for movies)
            
        Returns:
            Dictionary with 'streams' and 'subtitles' lists
        """
        streams, subs = [], []
        
        async def sub_cb(s): 
            subs.append(s)
            
        async def str_cb(s): 
            streams.append(s)
        
        # Run all providers concurrently
        provider_tasks = []
        for provider in self.providers:
            try:
                task = provider.get_streams(details, season, episode, sub_cb, str_cb)
                provider_tasks.append(task)
            except Exception as e:
                print(f"[SoraExtractor] Error creating task for {provider.name}: {e}")
        
        # Wait for all providers to complete
        if provider_tasks:
            await asyncio.gather(*provider_tasks, return_exceptions=True)
        
        # Sort streams by quality (highest first)
        streams.sort(key=lambda x: x.quality if hasattr(x, 'quality') and x.quality is not None else 0, reverse=True)
        
        return {"streams": streams, "subtitles": subs}
    
    # Legacy methods for backward compatibility
    async def invoke_vidsrc(self, details: Dict, season: Optional[int], episode: Optional[int],
                           subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """Legacy method - use VidSrc provider directly."""
        vidsrc_provider = registry.get_provider_by_name("VidSrc", self.session)
        if vidsrc_provider:
            await vidsrc_provider.get_streams(details, season, episode, subtitle_callback, callback)
    
    async def invoke_arabseed(self, details: Dict, season: Optional[int], episode: Optional[int],
                             subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """Legacy method - use ArabSeed provider directly."""
        arabseed_provider = registry.get_provider_by_name("ArabSeed", self.session)
        if arabseed_provider:
            await arabseed_provider.get_streams(details, season, episode, subtitle_callback, callback)
