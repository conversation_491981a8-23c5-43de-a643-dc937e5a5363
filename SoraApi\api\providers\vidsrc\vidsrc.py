import asyncio
import re
from typing import Dict, Optional
from urllib.parse import urljoin, urlparse
import aiohttp
from bs4 import BeautifulSoup

from ... import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback


class VidSrcProvider(BaseProvider):
    """
    VidSrc streaming provider with cloudnestra/prorcp flow, IMDb support for TV,
    friendly quality labels and detailed logging.
    """

    def __init__(self, session: aiohttp.ClientSession):
        super().__init__(session)
        self.name = "VidSrc"
        self.main_url = utils.VIDSRC_API
        self.opensubtitles_api_key = "uAs2wKWO43KbIpXMDQV40I0q5FeTPr9Y"

    # -------------------- Subtitles --------------------
    async def _fetch_opensubtitles(self, details: Dict, subtitle_callback: SubtitleCallback,
                                   season: Optional[int] = None, episode: Optional[int] = None):
        try:
            raw_imdb_id = details.get("imdb_id") or details.get("external_ids", {}).get("imdb_id")
            if not raw_imdb_id:
                print(f"[{self.name}] no imdb_id available for subtitles")
                return

            imdb_id = raw_imdb_id.replace("tt", "")
            params = {"imdb_id": imdb_id, "languages": "en,ar,ko"}
            if season:
                params["season_number"] = season
            if episode:
                params["episode_number"] = episode

            headers = {
                'Api-Key': self.opensubtitles_api_key,
                'User-Agent': 'SoraStream/1.0 (+https://example)'
            }

            async with self.session.get(
                "https://api.opensubtitles.com/api/v1/subtitles",
                params=params,
                headers=headers,
                timeout=4
            ) as response:
                if response.status != 200:
                    print(f"[{self.name}] OpenSubtitles returned {response.status}")
                    return

                data = await response.json()
                lang_subs = {}

                for sub in data.get('data', []):
                    attrs = sub.get('attributes', {})
                    lang_code = attrs.get('language')
                    if not lang_code:
                        continue

                    file_id = next((f.get('file_id') for f in attrs.get('files', []) if f.get('file_id')), None)
                    if not file_id:
                        continue

                    score = (not attrs.get('hearing_impaired', False), attrs.get('download_count', 0))
                    if lang_code not in lang_subs or score > lang_subs[lang_code]['score']:
                        lang_subs[lang_code] = {
                            'score': score,
                            'url': f"/subtitles/new_{file_id}.vtt",
                            'lang_name': attrs.get('language_name', lang_code.capitalize())
                        }

                for lang, sub_info in lang_subs.items():
                    print(f"[{self.name}] OpenSubtitles found {lang} -> {sub_info['url']}")
                    await subtitle_callback(utils.SubtitleFile(
                        lang=sub_info['lang_name'],
                        url=sub_info['url']
                    ))

        except Exception as e:
            print(f"[{self.name}] Subtitles API request failed: {e}")

    # -------------------- Quality mapping --------------------
    def map_quality_label(self, numeric: Optional[int], height: Optional[int] = None,
                          res_line: str = "", rel_url: str = "", reference_text: str = "") -> str:
        """
        Convert numeric height / odd numeric quality values to user-friendly labels.
        """
        if height and height > 0:
            return f"{height}p"

        combined = " ".join([res_line or "", rel_url or "", reference_text or ""])
        m = re.search(r'(\d{3,4})p', combined, flags=re.IGNORECASE)
        if m:
            return f"{m.group(1)}p"

        m2 = re.search(r'(\d{2,4})[x×](\d{2,4})', combined)
        if m2:
            try:
                return f"{int(m2.group(2))}p"
            except Exception:
                pass

        # numeric heuristic
        num_match = re.search(r'\b(\d{2,4})\b', combined)
        val = numeric
        if val is None and num_match:
            try:
                val = int(num_match.group(1))
            except Exception:
                val = None

        if val is not None:
            if val >= 2000: return "2160p"
            if val >= 1500: return "1440p"
            if val >= 1000: return "1080p"
            if val >= 700: return "720p"
            if val >= 450: return "480p"
            if val >= 300: return "360p"
            if val >= 150: return "240p"

        return "auto"

    # -------------------- M3U8 parse + emit --------------------
    async def _parse_and_emit_m3u8(self, m3u8_text: str, m3u8_url: str, referer: str,
                                   server_name: str, callback: ExtractorCallback) -> int:
        variants = re.findall(r'#EXT-X-STREAM-INF:(.*)\n(.*)', m3u8_text)
        if variants:
            count = 0
            for res_line, rel_url in variants:
                height_match = re.search(r'RESOLUTION=\d+x(\d+)', res_line)
                height = int(height_match.group(1)) if height_match else None
                bw_match = re.search(r'BANDWIDTH=(\d+)', res_line)
                bandwidth_kbps = int(int(bw_match.group(1)) / 1000) if bw_match else None
                label = self.map_quality_label(bandwidth_kbps or None, height, res_line, rel_url, m3u8_text)
                full_url = urljoin(m3u8_url, rel_url.strip())
                quality_field = height or (bandwidth_kbps or 0)
                name = f"VidSrc ({label})"
                print(f"[{self.name}] emitting variant {name} -> {full_url} (quality_field={quality_field})")
                try:
                    await callback(utils.ExtractorLink(
                        name=name,
                        url=full_url,
                        referer=referer,
                        quality=quality_field,
                        is_m3u8=True
                    ))
                    count += 1
                except Exception as e:
                    print(f"[{self.name}] callback error for {full_url}: {e}")
            return count
        else:
            # single playlist fallback
            label = self.map_quality_label(None, None, "", m3u8_url, m3u8_text)
            name = f"VidSrc ({label})"
            print(f"[{self.name}] emitting single playlist {name} -> {m3u8_url}")
            try:
                await callback(utils.ExtractorLink(
                    name=name,
                    url=m3u8_url,
                    referer=referer,
                    quality=0,
                    is_m3u8=True
                ))
                return 1
            except Exception as e:
                print(f"[{self.name}] callback error for single playlist {m3u8_url}: {e}")
                return 0

    # -------------------- Main extraction (cloudnestra/prorcp) --------------------
    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                          subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """
        Flow:
         - Build embed URLs (prefer IMDb for TV)
         - Fetch embed page(s)
         - Extract cloudnestra rcp or /prorcp path (iframe or in JS)
         - Fetch rcp -> find nested iframe or /prorcp
         - Fetch prorcp -> find master.m3u8 or direct m3u8
         - Fetch master.m3u8 -> parse and emit with friendly labels
        """
        print(f"[{self.name}] Trying VidSrc...")
        tmdb_id = details.get("id")
        imdb_id = details.get("imdb_id") or details.get("external_ids", {}).get("imdb_id")
        if not tmdb_id and not imdb_id:
            print(f"[{self.name}] no tmdb or imdb id in details, aborting")
            return

        subtitle_task = asyncio.create_task(self._fetch_opensubtitles(details, subtitle_callback, season, episode))
        try:
            CLOUD_REFERER = "https://cloudnestra.com/"
            ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120 Safari/537.36"
            media_type = "tv" if season is not None else "movie"

            # Build candidate embed URLs (try many patterns)
            embed_urls = []
            if media_type == "tv" and season is not None and episode is not None:
                # prefer imdb param if available
                if imdb_id:
                    embed_urls.append(f"{self.main_url}/embed/tv?imdb={imdb_id}&season={season}&episode={episode}&color=e600e6")
                    embed_urls.append(f"{self.main_url}/embed/tv?imdb={imdb_id}&season={season}&episode={episode}")
                # common path-style
                if tmdb_id:
                    embed_urls.append(f"{self.main_url}/embed/tv/{tmdb_id}/{season}/{episode}")
                # fallback patterns
                embed_urls.append(f"{self.main_url}/embed/tv/{tmdb_id or ''}")
                embed_urls.append(f"{self.main_url}/embed/tv?tmdb={tmdb_id or ''}&season={season}&episode={episode}")
                embed_urls.append(f"{self.main_url}/embed/tv?tmdb={tmdb_id or ''}")
            else:
                # movie
                if imdb_id:
                    embed_urls.append(f"{self.main_url}/embed/movie?imdb={imdb_id}&color=e600e6")
                    embed_urls.append(f"{self.main_url}/embed/movie?imdb={imdb_id}")
                if tmdb_id:
                    embed_urls.append(f"{self.main_url}/embed/movie/{tmdb_id}")
                    embed_urls.append(f"{self.main_url}/embed/movie?tmdb={tmdb_id}")
                embed_urls.append(f"{self.main_url}/embed/movie/{tmdb_id or ''}")

            # dedupe while preserving order
            seen = set()
            embed_urls_clean = []
            for u in embed_urls:
                if not u:
                    continue
                if u in seen:
                    continue
                seen.add(u)
                embed_urls_clean.append(u)
            embed_urls = embed_urls_clean

            embed_html = None
            used_embed = None
            for u in embed_urls:
                try:
                    print(f"[{self.name}] fetching embed page: {u}")
                    async with self.session.get(u, headers={"User-Agent": ua}, timeout=10) as resp:
                        status = resp.status
                        text = await resp.text()
                        print(f"[{self.name}] got {status} from {u} (len={len(text)})")
                        if status == 200 and text and len(text) > 200:
                            embed_html = text
                            used_embed = u
                            break
                        else:
                            print(f"[{self.name}] embed page not usable (status={status}, len={len(text)}) for {u}")
                except Exception as e:
                    print(f"[{self.name}] error fetching embed {u}: {e}")

            if not embed_html:
                print(f"[{self.name}] failed to fetch any embed page")
                await subtitle_task
                return

            embed_soup = BeautifulSoup(embed_html, "html.parser")

            # 1) Try direct iframe#player_iframe
            rcp_url = None
            iframe_tag = embed_soup.find("iframe", id="player_iframe")
            if iframe_tag and iframe_tag.get("src"):
                iframe_src = iframe_tag["src"].strip()
                print(f"[{self.name}] found iframe#player_iframe src: {iframe_src[:200]}...")
                if iframe_src.startswith("//"):
                    rcp_url = "https:" + iframe_src
                elif iframe_src.startswith("/"):
                    rcp_url = urljoin(self.main_url, iframe_src)
                else:
                    rcp_url = iframe_src

            # 2) fallback: any cloudnestra iframe
            if not rcp_url:
                iframe_any = embed_soup.find("iframe", src=re.compile(r"(cloudnestra|rcp)"))
                if iframe_any and iframe_any.get("src"):
                    src = iframe_any["src"].strip()
                    print(f"[{self.name}] found cloudnestra iframe src (fallback): {src[:200]}...")
                    if src.startswith("//"):
                        rcp_url = "https:" + src
                    elif src.startswith("/"):
                        rcp_url = urljoin(self.main_url, src)
                    else:
                        rcp_url = src

            # 3) fallback: /prorcp/ inside JS (e.g. src: '/prorcp/....')
            if not rcp_url:
                m = re.search(r"['\"](\/prorcp\/[A-Za-z0-9_\-:]+)['\"]", embed_html)
                if m:
                    rcp_url = urljoin("https://cloudnestra.com", m.group(1))
                    print(f"[{self.name}] found /prorcp/ in embed JS -> {rcp_url}")

            # 4) fallback: sometimes JS builds iframe via loadIframe() with src: '/prorcp/...' literal
            if not rcp_url:
                m2 = re.search(r"src\s*:\s*['\"](\/prorcp\/[A-Za-z0-9_\-:]+)['\"]", embed_html)
                if m2:
                    rcp_url = urljoin("https://cloudnestra.com", m2.group(1))
                    print(f"[{self.name}] found prorcp pattern in JS -> {rcp_url}")

            if not rcp_url:
                print(f"[{self.name}] No rcp/prorcp URL found in embed HTML")
                await subtitle_task
                return

            print(f"[{self.name}] resolved rcp url: {rcp_url}")

            headers_cloud = {
                "Referer": CLOUD_REFERER,
                "User-Agent": ua,
                "Host": urlparse(rcp_url).netloc
            }

            # fetch rcp page
            try:
                async with self.session.get(rcp_url, headers=headers_cloud, ssl=False, timeout=12) as rcp_resp:
                    if rcp_resp.status != 200:
                        print(f"[{self.name}] cloudnestra rcp returned {rcp_resp.status} for {rcp_url}")
                        await subtitle_task
                        return
                    rcp_text = await rcp_resp.text()
                    print(f"[{self.name}] fetched rcp page (len={len(rcp_text)})")
            except Exception as e:
                print(f"[{self.name}] error fetching rcp page {rcp_url}: {e}")
                await subtitle_task
                return

            # inside rcp page: look for nested iframe or /prorcp path
            prorcp_url = None
            nested_iframe_match = re.search(r'<iframe[^>]+src=["\']([^"\']+)["\']', rcp_text)
            if nested_iframe_match:
                nested_src = nested_iframe_match.group(1)
                print(f"[{self.name}] found nested iframe src in rcp: {nested_src[:200]}...")
                if nested_src.startswith("//"):
                    prorcp_url = "https:" + nested_src
                elif nested_src.startswith("/"):
                    parsed = urlparse(rcp_url)
                    base = f"{parsed.scheme}://{parsed.netloc}"
                    prorcp_url = urljoin(base, nested_src)
                else:
                    prorcp_url = nested_src
            else:
                m = re.search(r"['\"](\/prorcp\/[A-Za-z0-9_\-:]+)['\"]", rcp_text)
                if m:
                    parsed = urlparse(rcp_url)
                    base = f"{parsed.scheme}://{parsed.netloc}"
                    prorcp_url = urljoin(base, m.group(1))
                    print(f"[{self.name}] found prorcp path in rcp_text -> {prorcp_url}")

            if not prorcp_url:
                print(f"[{self.name}] could not find nested iframe nor prorcp path in rcp page")
                await subtitle_task
                return

            print(f"[{self.name}] resolved prorcp url: {prorcp_url}")

            headers_prorcp = headers_cloud.copy()
            headers_prorcp["Host"] = urlparse(prorcp_url).netloc

            try:
                async with self.session.get(prorcp_url, headers=headers_prorcp, ssl=False, timeout=12) as prorcp_resp:
                    if prorcp_resp.status != 200:
                        print(f"[{self.name}] prorcp returned {prorcp_resp.status} for {prorcp_url}")
                        await subtitle_task
                        return
                    prorcp_text = await prorcp_resp.text()
                    print(f"[{self.name}] fetched prorcp page (len={len(prorcp_text)})")
            except Exception as e:
                print(f"[{self.name}] error fetching prorcp page {prorcp_url}: {e}")
                await subtitle_task
                return

            # find master.m3u8 or any m3u8
            file_match = re.search(r"file\s*:\s*['\"](https?://[^'\"]+?master\.m3u8[^'\"]*)['\"]", prorcp_text)
            if not file_match:
                # fallback: any m3u8 link
                file_match = re.search(r"(https?://[^\s'\"<>]+\.m3u8[^\s'\"<>]*)", prorcp_text)
            if not file_match:
                print(f"[{self.name}] could not find master.m3u8 in prorcp page (searching the page for m3u8)")
                # sometimes the prorcp page contains embedded player config that base64/decompresses -> optional future improvement
                await subtitle_task
                return

            master_m3u8 = file_match.group(1)
            print(f"[{self.name}] found master.m3u8: {master_m3u8}")

            headers_m3u8 = {
                "Referer": CLOUD_REFERER,
                "User-Agent": ua,
                "Host": urlparse(master_m3u8).netloc
            }

            try:
                async with self.session.get(master_m3u8, headers=headers_m3u8, ssl=False, timeout=12) as m3u8_resp:
                    if m3u8_resp.status != 200:
                        print(f"[{self.name}] master.m3u8 request returned {m3u8_resp.status} for {master_m3u8}")
                        await subtitle_task
                        return
                    m3u8_text = await m3u8_resp.text()
                    print(f"[{self.name}] fetched master.m3u8 (len={len(m3u8_text)})")
            except Exception as e:
                print(f"[{self.name}] error fetching master.m3u8 {master_m3u8}: {e}")
                await subtitle_task
                return

            emitted = await self._parse_and_emit_m3u8(m3u8_text, master_m3u8, CLOUD_REFERER, "cloudnestra", callback)
            print(f"[{self.name}] done. emitted={emitted}")

        except Exception as e:
            print(f"[{self.name}] extraction error: {e}")
        finally:
            await subtitle_task
